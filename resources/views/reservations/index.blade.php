@extends('layouts.admin')

@section('title', 'Reservations - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservations Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Reservations</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-primary-transparent">
                                <i class="ti ti-calendar-event fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Upcoming Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $upcomingCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
            <div class="card custom-card">
                <div class="card-body">
                    <div class="d-flex align-items-top">
                        <div class="me-3">
                            <span class="avatar avatar-md bg-success-transparent">
                                <i class="ti ti-check fs-16"></i>
                            </span>
                        </div>
                        <div class="flex-fill">
                            <div class="d-flex align-items-center justify-content-between mb-1">
                                <h6 class="mb-0">Total Reservations</h6>
                            </div>
                            <div class="d-flex align-items-center justify-content-between">
                                <div>
                                    <span class="h4 fw-semibold mb-0">{{ $totalReservationsCount }}</span>
                                </div>
                                <div class="text-success">
                                    <i class="ti ti-trending-up me-1"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Reservations
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.create') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-plus me-1"></i>New Reservation
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>

                <!---------------------------------------------------------------------------------------------------------------------->
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('reservations.index') }}" class="px-4">
                    <div class="row bg-light rounded mt-3 mb-0 py-2 align-items-center">
                        <div class="col-xl-3">
                            <div class="d-flex align-items-center gap-2">
                                <label class="form-label mb-0 fw-semibold text-muted">
                                    <i class="ti ti-filter me-1"></i>Filters:
                                </label>
                                <select name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    @foreach (\App\Models\Booking::getStatuses() as $key => $status)
                                        <option value="{{ $key }}" {{ request('status') === $key ? 'selected' : '' }}>
                                            {{ $status }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-xl-2">
                            <select name="field_id" class="form-select">
                                <option value="">All Fields</option>
                                @foreach ($fields as $field)
                                    <option value="{{ $field->id }}"
                                        {{ request('field_id') == $field->id ? 'selected' : '' }}>{{ $field->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-xl-2">
                            <x-custom-date-picker
                                name="date_from"
                                id="date_from"
                                :value="request('date_from')"
                                placeholder="From Date"
                                :show-label="false"
                                class="pt-1 pb-1" />
                        </div>
                        <div class="col-xl-2">
                            <x-custom-date-picker
                                name="date_to"
                                id="date_to"
                                :value="request('date_to')"
                                placeholder="To Date"
                                :show-label="false"
                                class="pt-1 pb-1" />
                        </div>
                        <div class="col-xl-3 mt-0">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-info pt-1 pb-1 flex-fill">
                                    <i class="ti ti-search me-1"></i>Filter
                                </button>
                                <a href="{{ route('reservations.index') }}" class="btn btn-info pt-1 pb-1 flex-fill">
                                    <i class="ti ti-refresh me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
                <!---------------------------------------------------------------------------------------------------------------------->


                <div class="card-body">
                    @if ($reservations->count() > 0)
                        <div class="table-responsive">
                            <table class="table text-nowrap table-hover">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Date & Time</th>
                                        <th>Customer</th>
                                        <th>Cost</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($reservations as $reservation)
                                        <tr class="clickable-row"
                                            data-url="{{ route('reservations.show', $reservation) }}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-2">
                                                        <span class="avatar avatar-sm bg-dark-transparent text-dark">
                                                            <i class="{{ $reservation->field->icon ?? 'bx bx-stadium' }}" style="font-size: 1.2rem;"></i>
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <span class="fw-semibold">{{ $reservation->field->name }}</span>
                                                        <br>
                                                        <small class="text-muted">{{ $reservation->field->type }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <span
                                                        class="fw-semibold">{{ $reservation->booking_date->format('M d, Y') }}</span>
                                                    <br>
                                                    <small class="text-muted">{{ $reservation->time_range }}</small>
                                                    <br>
                                                    <span class="badge bg-primary-transparent text-primary">Duration:
                                                        {{ $reservation->duration_hours }}
                                                        {{ Str::plural('hour', $reservation->duration_hours) }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-semibold">{{ $reservation->customer_display_name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-semibold text-success">XCG
                                                    {{ number_format($reservation->total_cost, 2) }}</span>
                                            </td>
                                            <td>
                                                <span
                                                    class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }}">
                                                    {{ $reservation->status }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    {{-- View Details: Allow admins, members, and reservation owners --}}
                                                    @if (auth()->user()->isAdmin() || auth()->user()->isMember() || $reservation->user_id === auth()->id())
                                                        <a href="{{ route('reservations.show', $reservation) }}"
                                                            class="btn btn-sm btn-info" title="View Details">
                                                            <i class="ti ti-eye"></i>
                                                        </a>
                                                    @endif

                                                    {{-- Edit/Cancel Actions: Only for admins and reservation owners --}}
                                                    @if (auth()->user()->isAdmin() || $reservation->user_id === auth()->id())
                                                        @if ($reservation->canBeModified())
                                                            <a href="{{ route('reservations.edit', $reservation) }}"
                                                                class="btn btn-sm btn-warning" title="Edit">
                                                                <i class="ti ti-edit"></i>
                                                            </a>
                                                        @endif

                                                        {{-- Cancel/Restore Actions --}}
                                                        @if ($reservation->isCancelled() && $reservation->canBeUncancelled())
                                                            <button type="button" class="btn btn-sm btn-primary restore-reservation-btn"
                                                                title="Restore"
                                                                onclick="confirmRestoreReservation({{ $reservation->id }}, '{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}')">
                                                                <i class="bx bx-undo"></i>
                                                            </button>
                                                        @elseif ($reservation->canBeCancelled())
                                                            <button type="button"
                                                                class="btn btn-sm btn-danger cancel-reservation-form cancel-reservation-btn"
                                                                title="Cancel"
                                                                data-cancel-url="{{ route('reservations.cancel', $reservation) }}"
                                                                data-reservation-details="{{ $reservation->field->name }} - {{ $reservation->booking_date->format('M j, Y') }} {{ $reservation->time_range }}">
                                                                <i class="ti ti-x"></i>
                                                            </button>
                                                        @endif
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $reservations->firstItem() }} to {{ $reservations->lastItem() }}
                                    of {{ $reservations->total() }} reservations
                                </div>
                                <div class="admin-pagination">
                                    <x-pagination :paginator="$reservations" />
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="ti ti-calendar-x fs-48 text-muted"></i>
                            </div>

                            @if (request()->hasAny(['status', 'field_id', 'date_from', 'date_to']) &&
                                    (request('status') || request('field_id') || request('date_from') || request('date_to')))
                                <h5 class="text-muted">No Reservations Match Your Filters
                                </h5>
                                <p class="text-muted">Try adjusting the filter options.</p>
                                <a href="{{ route('reservations.index') }}" class="btn btn-secondary">
                                    <i class="ti ti-refresh me-1"></i>Clear Filters
                                </a>
                            @else
                                <h5 class="text-muted">No Reservations Found</h5>
                                <p class="text-muted">You haven't made any reservations yet.</p>
                                <a href="{{ route('reservations.create') }}" class="btn btn-primary">
                                    <i class="ti ti-plus me-1"></i>Make Your First Reservation
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Restore Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="restoreReservationModal"
        type="success"
        icon="bx bx-undo"
        modal-title="Confirm Restore"
        title="Are you sure you want to restore the reservation for &quot;<span id='restoreReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="This will reactivate the reservation and make it available again."
        cancel-text="No, Keep Cancelled"
        confirm-text="Yes, Restore Reservation"
        form-action="#"
        form-method="POST"
    />

    <!-- Cancel Reservation Confirmation Modal -->
    <x-confirmation-modal
        modal-id="cancelReservationModal"
        type="danger"
        icon="ri-alert-fill"
        modal-title="Confirm Cancel"
        title="Are you sure you want to cancel the reservation for &quot;<span id='cancelReservationDetails' class='fw-semibold'></span>&quot;?"
        warning-text="The reservation will be cancelled."
        cancel-text="No, Keep Reservation"
        confirm-text="Yes, Cancel Reservation"
        form-action="#"
        form-method="POST"
    />
@endsection

@push('scripts')
    <style>
        /* Ensure proper button group styling for dynamically created buttons */
        .btn-group .btn:not(:first-child) {
            margin-left: -1px !important;
        }

        .btn-group .btn:first-child:not(:last-child) {
            border-top-right-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
        }

        .btn-group .btn:last-child:not(:first-child) {
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }

        .btn-group .btn:not(:first-child):not(:last-child) {
            border-radius: 0 !important;
        }

        /* Force proper z-index for button group buttons */
        .btn-group .btn:hover,
        .btn-group .btn:focus,
        .btn-group .btn:active {
            z-index: 2;
        }
    </style>
    <script>
        // make table row clickable:
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.clickable-row');

            rows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Prevent triggering row click when clicking a button or link
                    if (e.target.closest('a') || e.target.closest('button') || e.target.closest(
                            'form')) {
                        return;
                    }
                    const url = row.dataset.url;
                    if (url) {
                        window.location.href = url;
                    }
                });
            });
        });

        /**
         * Utility function to show dynamic notifications
         */
        function showNotification(message, type = 'success') {
            // Remove any existing notifications
            const existingAlerts = document.querySelectorAll('.dynamic-alert');
            existingAlerts.forEach(alert => alert.remove());

            // Create new alert element
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show dynamic-alert`;
            alertDiv.setAttribute('role', 'alert');
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '10px';
            alertDiv.style.left = '50%';
            alertDiv.style.transform = 'translateX(-50%)';
            alertDiv.style.zIndex = '9999';
            alertDiv.style.minWidth = '300px';
            alertDiv.style.maxWidth = '500px';

            const iconClass = type === 'success' ? 'ti-check-circle' : 'ti-exclamation-triangle';
            alertDiv.innerHTML = `
                <i class="ti ${iconClass} me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add to page
            document.body.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        /**
         * Update reservation row UI after status change
         */
        function updateReservationRow(reservationId, reservationData) {
            const row = document.querySelector(`tr[data-url*="reservations/${reservationId}"]`);
            if (!row) return;

            // Update status badge
            const statusBadge = row.querySelector('td:nth-child(5) .badge');
            if (statusBadge) {
                statusBadge.className = `badge bg-${reservationData.status_color}-transparent text-${reservationData.status_color}`;
                statusBadge.textContent = reservationData.status;
            }

            // Update action buttons
            const actionsCell = row.querySelector('td:last-child .btn-group');
            if (actionsCell) {
                updateActionButtons(actionsCell, reservationId, reservationData);
            }
        }

        /**
         * Fix Bootstrap button group styling for dynamically added buttons
         * This approach clones and replaces the entire button group to force Bootstrap recalculation
         */
        function fixButtonGroupStyling(actionsCell) {
            const btnGroup = actionsCell.closest('.btn-group');
            if (!btnGroup) {
                return;
            }

            try {
                // Store the parent element and position
                const parent = btnGroup.parentNode;
                const nextSibling = btnGroup.nextSibling;

                // Clone the button group
                const newBtnGroup = btnGroup.cloneNode(true);

                // Remove the old button group
                btnGroup.remove();

                // Insert the cloned button group back
                if (nextSibling) {
                    parent.insertBefore(newBtnGroup, nextSibling);
                } else {
                    parent.appendChild(newBtnGroup);
                }

                // Re-attach event listeners to the new buttons
                reattachButtonEventListeners(newBtnGroup);

                // Force a reflow to ensure CSS is applied
                newBtnGroup.offsetHeight;
            } catch (error) {
                console.error('Error fixing button group styling:', error);
                // Fallback: apply manual CSS styling
                applyManualButtonGroupStyling(actionsCell);
            }
        }

        /**
         * Fallback manual CSS styling for button groups
         */
        function applyManualButtonGroupStyling(actionsCell) {
            const btnGroup = actionsCell.closest('.btn-group');
            if (!btnGroup) return;

            const buttons = Array.from(btnGroup.querySelectorAll('.btn'));

            buttons.forEach((btn, index) => {
                // Reset any existing styles
                btn.style.borderRadius = '';
                btn.style.marginLeft = '';

                if (buttons.length === 1) {
                    // Single button - keep default border radius
                    btn.style.borderRadius = '0.375rem';
                } else if (index === 0) {
                    // First button
                    btn.style.borderTopRightRadius = '0';
                    btn.style.borderBottomRightRadius = '0';
                } else if (index === buttons.length - 1) {
                    // Last button
                    btn.style.borderTopLeftRadius = '0';
                    btn.style.borderBottomLeftRadius = '0';
                    btn.style.marginLeft = '-1px';
                } else {
                    // Middle buttons
                    btn.style.borderRadius = '0';
                    btn.style.marginLeft = '-1px';
                }
            });
        }

        /**
         * Re-attach event listeners to buttons after cloning
         */
        function reattachButtonEventListeners(btnGroup) {
            // Re-attach cancel button event listeners
            const cancelBtns = btnGroup.querySelectorAll('.cancel-reservation-btn');
            cancelBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const cancelUrl = this.dataset.cancelUrl || this.getAttribute('data-cancel-url');
                    const reservationDetails = this.closest('tr').querySelector('td:nth-child(1) .fw-semibold').textContent +
                                             ' - ' + this.closest('tr').querySelector('td:nth-child(2) .fw-semibold').textContent;
                    confirmCancelReservation(cancelUrl, reservationDetails);
                });
            });

            // Re-attach restore button event listeners
            const restoreBtns = btnGroup.querySelectorAll('.restore-reservation-btn');
            restoreBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const reservationId = extractReservationIdFromRow(this.closest('tr'));
                    const reservationDetails = this.closest('tr').querySelector('td:nth-child(1) .fw-semibold').textContent +
                                             ' - ' + this.closest('tr').querySelector('td:nth-child(2) .fw-semibold').textContent;
                    confirmRestoreReservation(reservationId, reservationDetails);
                });
            });
        }

        /**
         * Extract reservation ID from table row
         */
        function extractReservationIdFromRow(row) {
            const url = row.dataset.url;
            if (url) {
                const matches = url.match(/\/reservations\/(\d+)/);
                return matches ? matches[1] : null;
            }
            return null;
        }

        /**
         * Update action buttons based on reservation status
         */
        function updateActionButtons(actionsCell, reservationId, reservationData) {
            // Find existing buttons
            const editBtn = actionsCell.querySelector('a[href*="edit"]');
            const cancelBtn = actionsCell.querySelector('.cancel-reservation-btn');
            const restoreBtn = actionsCell.querySelector('.restore-reservation-btn');

            // Handle edit button visibility - show only if reservation can be modified
            if (editBtn) {
                if (reservationData.can_be_modified) {
                    editBtn.style.display = 'inline-block';
                } else {
                    editBtn.style.display = 'none';
                }
            }

            if (reservationData.status === 'Cancelled' && reservationData.can_be_uncancelled) {
                // Show restore button, hide cancel button
                if (cancelBtn) cancelBtn.style.display = 'none';
                if (restoreBtn) {
                    restoreBtn.style.display = 'inline-block';
                } else {
                    // Create restore button if it doesn't exist
                    const newRestoreBtn = document.createElement('button');
                    newRestoreBtn.type = 'button';
                    newRestoreBtn.className = 'btn btn-sm btn-primary restore-reservation-btn';
                    newRestoreBtn.title = 'Restore';
                    newRestoreBtn.innerHTML = '<i class="bx bx-undo"></i>';

                    newRestoreBtn.onclick = function() {
                        const reservationDetails = this.closest('tr').querySelector('td:nth-child(1) .fw-semibold').textContent +
                                                 ' - ' + this.closest('tr').querySelector('td:nth-child(2) .fw-semibold').textContent;
                        confirmRestoreReservation(reservationId, reservationDetails);
                    };
                    actionsCell.appendChild(newRestoreBtn);

                    // Fix Bootstrap button group styling by rebuilding the entire button group
                    setTimeout(() => {
                        fixButtonGroupStyling(actionsCell);
                    }, 10);
                }
            } else if (reservationData.can_be_cancelled) {
                // Show cancel button, hide restore button
                if (restoreBtn) restoreBtn.style.display = 'none';
                if (cancelBtn) {
                    cancelBtn.style.display = 'inline-block';
                } else {
                    // Create cancel button if it doesn't exist
                    const newCancelBtn = document.createElement('button');
                    newCancelBtn.type = 'button';
                    newCancelBtn.className = 'btn btn-sm btn-danger cancel-reservation-form cancel-reservation-btn';
                    newCancelBtn.title = 'Cancel';
                    newCancelBtn.innerHTML = '<i class="ti ti-x"></i>';
                    newCancelBtn.setAttribute('data-cancel-url', `/reservations/${reservationId}/cancel`);

                    newCancelBtn.onclick = function() {
                        const reservationDetails = this.closest('tr').querySelector('td:nth-child(1) .fw-semibold').textContent +
                                                 ' - ' + this.closest('tr').querySelector('td:nth-child(2) .fw-semibold').textContent;
                        confirmCancelReservation(`/reservations/${reservationId}/cancel`, reservationDetails);
                    };
                    actionsCell.appendChild(newCancelBtn);

                    // Fix Bootstrap button group styling by rebuilding the entire button group
                    setTimeout(() => {
                        fixButtonGroupStyling(actionsCell);
                    }, 10);
                }
            } else {
                // Hide both buttons if neither action is available
                if (cancelBtn) cancelBtn.style.display = 'none';
                if (restoreBtn) restoreBtn.style.display = 'none';
            }
        }

        /**
         * Dynamic Form Creation for Cancel Reservation
         *
         * This script replaces the static HTML forms with dynamically created forms
         * that are submitted via JavaScript while maintaining the same functionality
         * including CSRF protection and confirmation dialogs.
         */
        document.addEventListener('DOMContentLoaded', function() {
            // Attach click event listeners to all cancel reservation buttons
            document.querySelectorAll('.cancel-reservation-form').forEach(button => {
                button.addEventListener('click', function() {
                    const cancelUrl = this.dataset.cancelUrl;
                    const reservationDetails = this.dataset.reservationDetails;

                    // Show confirmation modal
                    confirmCancelReservation(cancelUrl, reservationDetails);
                });
            });

            // Use event delegation for modal form submissions to handle dynamically created modals
            document.addEventListener('submit', function(e) {
                if (e.target.id === 'cancelReservationModalForm') {
                    e.preventDefault();
                    const actionUrl = e.target.action;
                    const reservationId = actionUrl.split('/').slice(-2, -1)[0]; // Extract ID from URL
                    submitAjaxRequest(actionUrl, 'POST', reservationId, 'cancel');
                } else if (e.target.id === 'restoreReservationModalForm') {
                    e.preventDefault();
                    const actionUrl = e.target.action;
                    const reservationId = actionUrl.split('/').slice(-2, -1)[0]; // Extract ID from URL
                    submitAjaxRequest(actionUrl, 'POST', reservationId, 'restore');
                }
            });
        });

        /**
         * Submit AJAX request for reservation actions
         */
        function submitAjaxRequest(url, method, reservationId, action) {
            // Show loading state
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal.show'));

            // Find the submit button - look for the actual submit button first, then fallback to color classes
            let submitBtn = document.querySelector('.modal.show button[type="submit"]');

            // If no submit button found, try to find by common button classes
            if (!submitBtn) {
                submitBtn = document.querySelector('.modal.show .btn-success') ||
                           document.querySelector('.modal.show .btn-danger') ||
                           document.querySelector('.modal.show .btn-primary') ||
                           document.querySelector('.modal.show .btn-warning') ||
                           document.querySelector('.modal.show .btn-info');
            }

            if (!submitBtn) {
                console.error('Submit button not found in modal');
                showNotification('Error: Could not find submit button in modal', 'danger');
                return;
            }

            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="ti ti-loader-2 me-1"></i>Processing...';
            submitBtn.disabled = true;

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI
                    updateReservationRow(reservationId, data.reservation);

                    // Show success notification
                    showNotification(data.message, 'success');

                    // Close modal
                    modal.hide();
                } else {
                    // Show error notification
                    showNotification(data.error || 'An error occurred', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while processing your request', 'danger');
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        /**
         * Restore reservation confirmation function
         */
        function confirmRestoreReservation(reservationId, reservationDetails) {
            const detailsElement = document.getElementById('restoreReservationDetails');
            const formElement = document.getElementById('restoreReservationModalForm');
            const modalElement = document.getElementById('restoreReservationModal');

            if (!detailsElement || !formElement || !modalElement) {
                console.error('Missing modal elements for restore reservation');
                return;
            }

            detailsElement.textContent = reservationDetails;
            formElement.action = `/reservations/${reservationId}/uncancel`;

            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }

        /**
         * Cancel reservation confirmation function
         */
        function confirmCancelReservation(cancelUrl, reservationDetails) {
            document.getElementById('cancelReservationDetails').textContent = reservationDetails;
            document.getElementById('cancelReservationModalForm').action = cancelUrl;
            const modal = new bootstrap.Modal(document.getElementById('cancelReservationModal'));
            modal.show();
        }
    </script>
@endpush
