<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reservation Restore Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tabler-icons@latest/tabler-sprite.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .btn-group .btn:not(:first-child) {
            margin-left: -1px !important;
        }
        .btn-group .btn:first-child:not(:last-child) {
            border-top-right-radius: 0 !important;
            border-bottom-right-radius: 0 !important;
        }
        .btn-group .btn:last-child:not(:first-child) {
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }
        .btn-group .btn:not(:first-child):not(:last-child) {
            border-radius: 0 !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Reservation Restore Fix Test</h1>
        <p>This test demonstrates the fix for the Edit button not reappearing after restoring a cancelled reservation.</p>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Scenario</h5>
            </div>
            <div class="card-body">
                <p><strong>Issue:</strong> When a reservation is cancelled, the Edit button disappears (correctly). However, when the reservation is restored, the Edit button doesn't reappear because it was never rendered in the DOM for cancelled reservations.</p>
                
                <p><strong>Solution:</strong> Modified the JavaScript to dynamically create the Edit button when a reservation is restored and the user has permission to edit it.</p>
                
                <h6>Simulated Reservation Actions:</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Date & Time</th>
                                <th>Customer</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr id="test-reservation-row">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <span class="avatar avatar-sm bg-dark-transparent text-dark">
                                                <i class="ti ti-ball-football" style="font-size: 1.2rem;"></i>
                                            </span>
                                        </div>
                                        <div>
                                            <span class="fw-semibold">Football Field A</span>
                                            <br>
                                            <small class="text-muted">Football</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <span class="fw-semibold">Dec 25, 2024</span>
                                        <br>
                                        <small class="text-muted">14:00 - 16:00</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-semibold">John Doe</span>
                                </td>
                                <td>
                                    <span id="status-badge" class="badge bg-secondary-transparent text-secondary">Confirmed</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group" id="action-buttons">
                                        <a href="#" class="btn btn-sm btn-info" title="View Details">
                                            <i class="ti ti-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-warning" title="Edit" id="edit-btn">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" title="Cancel" id="cancel-btn">
                                            <i class="ti ti-x"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-danger me-2" onclick="simulateCancel()">Simulate Cancel</button>
                    <button class="btn btn-success me-2" onclick="simulateRestore()">Simulate Restore</button>
                    <button class="btn btn-secondary" onclick="resetTest()">Reset Test</button>
                </div>
                
                <div class="mt-3">
                    <h6>Test Results:</h6>
                    <div id="test-results" class="alert alert-info">
                        Ready to test. Click "Simulate Cancel" first, then "Simulate Restore" to see the fix in action.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simulate the updateActionButtons function from the actual code
        function updateActionButtons(actionsCell, reservationId, reservationData) {
            // Find existing buttons
            let editBtn = actionsCell.querySelector('a[href*="edit"], #edit-btn');
            const cancelBtn = actionsCell.querySelector('#cancel-btn, .cancel-reservation-btn');
            const restoreBtn = actionsCell.querySelector('#restore-btn, .restore-reservation-btn');

            // Handle edit button visibility - show only if reservation can be modified and user has permission
            if (reservationData.can_be_modified && reservationData.user_can_edit) {
                if (editBtn) {
                    editBtn.style.display = 'inline-block';
                } else {
                    // Create edit button if it doesn't exist
                    editBtn = document.createElement('a');
                    editBtn.href = '#';
                    editBtn.className = 'btn btn-sm btn-warning';
                    editBtn.title = 'Edit';
                    editBtn.id = 'edit-btn';
                    editBtn.innerHTML = '<i class="ti ti-edit"></i>';
                    
                    // Insert edit button as the second button (after view button)
                    const viewBtn = actionsCell.querySelector('.btn-info');
                    if (viewBtn && viewBtn.nextSibling) {
                        actionsCell.insertBefore(editBtn, viewBtn.nextSibling);
                    } else {
                        actionsCell.insertBefore(editBtn, actionsCell.firstChild);
                    }
                    
                    // Fix Bootstrap button group styling
                    setTimeout(() => {
                        fixButtonGroupStyling(actionsCell);
                    }, 10);
                }
            } else {
                if (editBtn) {
                    editBtn.style.display = 'none';
                }
            }

            if (reservationData.status === 'Cancelled' && reservationData.can_be_uncancelled) {
                // Show restore button, hide cancel button
                if (cancelBtn) cancelBtn.style.display = 'none';
                if (restoreBtn) {
                    restoreBtn.style.display = 'inline-block';
                } else {
                    // Create restore button if it doesn't exist
                    const newRestoreBtn = document.createElement('button');
                    newRestoreBtn.type = 'button';
                    newRestoreBtn.className = 'btn btn-sm btn-primary';
                    newRestoreBtn.title = 'Restore';
                    newRestoreBtn.id = 'restore-btn';
                    newRestoreBtn.innerHTML = '<i class="ti ti-arrow-back-up"></i>';
                    actionsCell.appendChild(newRestoreBtn);

                    // Fix Bootstrap button group styling
                    setTimeout(() => {
                        fixButtonGroupStyling(actionsCell);
                    }, 10);
                }
            } else if (reservationData.can_be_cancelled) {
                // Show cancel button, hide restore button
                if (restoreBtn) restoreBtn.style.display = 'none';
                if (cancelBtn) {
                    cancelBtn.style.display = 'inline-block';
                }
            } else {
                // Hide both buttons if neither action is available
                if (cancelBtn) cancelBtn.style.display = 'none';
                if (restoreBtn) restoreBtn.style.display = 'none';
            }
        }

        function fixButtonGroupStyling(actionsCell) {
            const btnGroup = actionsCell.closest('.btn-group');
            if (!btnGroup) return;

            const buttons = Array.from(btnGroup.querySelectorAll('.btn:not([style*="display: none"])'));

            buttons.forEach((btn, index) => {
                // Reset any existing styles
                btn.style.borderRadius = '';
                btn.style.marginLeft = '';

                if (buttons.length === 1) {
                    // Single button - keep default border radius
                    btn.style.borderRadius = '0.375rem';
                } else if (index === 0) {
                    // First button
                    btn.style.borderTopRightRadius = '0';
                    btn.style.borderBottomRightRadius = '0';
                } else if (index === buttons.length - 1) {
                    // Last button
                    btn.style.borderTopLeftRadius = '0';
                    btn.style.borderBottomLeftRadius = '0';
                    btn.style.marginLeft = '-1px';
                } else {
                    // Middle buttons
                    btn.style.borderRadius = '0';
                    btn.style.marginLeft = '-1px';
                }
            });
        }

        function simulateCancel() {
            const statusBadge = document.getElementById('status-badge');
            const actionsCell = document.getElementById('action-buttons');
            const resultsDiv = document.getElementById('test-results');
            
            // Update status
            statusBadge.className = 'badge bg-danger-transparent text-danger';
            statusBadge.textContent = 'Cancelled';
            
            // Simulate cancelled reservation data
            const reservationData = {
                status: 'Cancelled',
                can_be_modified: false,
                can_be_cancelled: false,
                can_be_uncancelled: true,
                user_can_edit: true
            };
            
            updateActionButtons(actionsCell, 123, reservationData);
            
            resultsDiv.className = 'alert alert-warning';
            resultsDiv.innerHTML = '<strong>Cancelled:</strong> Edit button is now hidden (correct behavior). Notice the Restore button appeared.';
        }

        function simulateRestore() {
            const statusBadge = document.getElementById('status-badge');
            const actionsCell = document.getElementById('action-buttons');
            const resultsDiv = document.getElementById('test-results');
            
            // Update status
            statusBadge.className = 'badge bg-secondary-transparent text-secondary';
            statusBadge.textContent = 'Confirmed';
            
            // Simulate restored reservation data
            const reservationData = {
                status: 'Confirmed',
                can_be_modified: true,
                can_be_cancelled: true,
                can_be_uncancelled: false,
                user_can_edit: true
            };
            
            updateActionButtons(actionsCell, 123, reservationData);
            
            resultsDiv.className = 'alert alert-success';
            resultsDiv.innerHTML = '<strong>Restored:</strong> Edit button is now visible again! The fix dynamically created the Edit button since it didn\'t exist in the DOM for cancelled reservations.';
        }

        function resetTest() {
            const statusBadge = document.getElementById('status-badge');
            const actionsCell = document.getElementById('action-buttons');
            const resultsDiv = document.getElementById('test-results');
            
            // Reset to initial state
            statusBadge.className = 'badge bg-secondary-transparent text-secondary';
            statusBadge.textContent = 'Confirmed';
            
            // Reset buttons to initial state
            actionsCell.innerHTML = `
                <a href="#" class="btn btn-sm btn-info" title="View Details">
                    <i class="ti ti-eye"></i>
                </a>
                <a href="#" class="btn btn-sm btn-warning" title="Edit" id="edit-btn">
                    <i class="ti ti-edit"></i>
                </a>
                <button type="button" class="btn btn-sm btn-danger" title="Cancel" id="cancel-btn">
                    <i class="ti ti-x"></i>
                </button>
            `;
            
            resultsDiv.className = 'alert alert-info';
            resultsDiv.innerHTML = 'Test reset. Ready to test again.';
        }
    </script>
</body>
</html>
